#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码管理路由
用于处理代码拉取和应用重启
"""

from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from services.code_management_service import code_management_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
code_management_bp = Blueprint('code_management', __name__, url_prefix='/code_management')

@code_management_bp.route('/')
@login_required
def code_management_page():
    """代码管理页面"""
    try:
        # 获取Git状态信息
        git_status = code_management_service.get_git_status()
        
        return render_template('code_management.html', 
                             git_status=git_status,
                             page_title='代码管理')
    except Exception as e:
        logger.error(f"加载代码管理页面失败: {str(e)}")
        return render_template('error.html', 
                             error_message=f'加载代码管理页面失败: {str(e)}'), 500

@code_management_bp.route('/api/git_status')
@login_required
def api_git_status():
    """获取Git状态API"""
    try:
        status = code_management_service.get_git_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取Git状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取Git状态失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/pull_code', methods=['POST'])
@login_required
def api_pull_code():
    """拉取最新代码API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求拉取最新代码")
        
        result = code_management_service.pull_latest_code()
        
        if result['success']:
            logger.info(f"用户 {current_user.username} 代码拉取成功")
        else:
            logger.warning(f"用户 {current_user.username} 代码拉取失败: {result.get('error', '未知错误')}")
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"拉取代码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'拉取代码失败: {str(e)}'
        }), 500



@code_management_bp.route('/api/git_branch_graph')
@login_required
def api_git_branch_graph():
    """获取Git分支图API"""
    try:
        graph_data = code_management_service.get_git_branch_graph()
        return jsonify(graph_data)
    except Exception as e:
        logger.error(f"获取Git分支图失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取Git分支图失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/flask_reload', methods=['POST'])
@login_required
def api_flask_reload():
    """Flask重载应用程序API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求Flask重载应用程序")

        result = code_management_service.trigger_flask_reload()

        if result['success']:
            logger.info(f"用户 {current_user.username} Flask重载成功")
        else:
            logger.warning(f"用户 {current_user.username} Flask重载失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"Flask重载失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Flask重载失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/update_and_reload', methods=['POST'])
@login_required
def api_update_and_reload():
    """更新代码并Flask重载API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求更新代码并Flask重载")

        result = code_management_service.update_and_reload()

        if result['success']:
            logger.info(f"用户 {current_user.username} 更新并Flask重载成功")
        else:
            logger.warning(f"用户 {current_user.username} 更新并Flask重载失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"更新代码并Flask重载失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'更新代码并Flask重载失败: {str(e)}'
        }), 500


