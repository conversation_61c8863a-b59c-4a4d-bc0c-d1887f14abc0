#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码管理路由
用于处理代码拉取和应用重启
"""

from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from services.code_management_service import code_management_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
code_management_bp = Blueprint('code_management', __name__, url_prefix='/code_management')

@code_management_bp.route('/')
@login_required
def code_management_page():
    """代码管理页面"""
    try:
        # 获取Git状态信息
        git_status = code_management_service.get_git_status()
        
        return render_template('code_management.html', 
                             git_status=git_status,
                             page_title='代码管理')
    except Exception as e:
        logger.error(f"加载代码管理页面失败: {str(e)}")
        return render_template('error.html', 
                             error_message=f'加载代码管理页面失败: {str(e)}'), 500

@code_management_bp.route('/api/git_status')
@login_required
def api_git_status():
    """获取Git状态API"""
    try:
        status = code_management_service.get_git_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取Git状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取Git状态失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/pull_code', methods=['POST'])
@login_required
def api_pull_code():
    """拉取最新代码API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求拉取最新代码")
        
        result = code_management_service.pull_latest_code()
        
        if result['success']:
            logger.info(f"用户 {current_user.username} 代码拉取成功")
        else:
            logger.warning(f"用户 {current_user.username} 代码拉取失败: {result.get('error', '未知错误')}")
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"拉取代码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'拉取代码失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/restart_app', methods=['POST'])
@login_required
def api_restart_app():
    """重启应用程序API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求重启应用程序")
        
        result = code_management_service.restart_application()
        
        if result['success']:
            logger.info(f"用户 {current_user.username} 应用重启请求已处理")
        else:
            logger.warning(f"用户 {current_user.username} 应用重启失败: {result.get('error', '未知错误')}")
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"重启应用程序失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'重启应用程序失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/git_branch_graph')
@login_required
def api_git_branch_graph():
    """获取Git分支图API"""
    try:
        graph_data = code_management_service.get_git_branch_graph()
        return jsonify(graph_data)
    except Exception as e:
        logger.error(f"获取Git分支图失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取Git分支图失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/hot_reload', methods=['POST'])
@login_required
def api_hot_reload():
    """热重载应用程序API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求热重载应用程序")

        result = code_management_service.hot_reload_application()

        if result['success']:
            logger.info(f"用户 {current_user.username} 热重载成功")
        else:
            logger.warning(f"用户 {current_user.username} 热重载失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"热重载失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'热重载失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/update_and_hot_reload', methods=['POST'])
@login_required
def api_update_and_hot_reload():
    """更新代码并热重载API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求更新代码并热重载")

        result = code_management_service.update_and_hot_reload()

        if result['success']:
            logger.info(f"用户 {current_user.username} 更新并热重载成功")
        else:
            logger.warning(f"用户 {current_user.username} 更新并热重载失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"更新代码并热重载失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'更新代码并热重载失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/update_and_restart', methods=['POST'])
@login_required
def api_update_and_restart():
    """更新代码并重启应用程序API（传统方式）"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求更新代码并重启应用程序")

        # 先拉取最新代码
        pull_result = code_management_service.pull_latest_code()

        if not pull_result['success']:
            return jsonify(pull_result)

        # 如果代码有更新，则重启应用
        if pull_result.get('updated', False):
            restart_result = code_management_service.restart_application()

            if restart_result['success']:
                return jsonify({
                    'success': True,
                    'message': '代码更新成功，应用程序正在重启...',
                    'pull_result': pull_result,
                    'restart_result': restart_result
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f"代码更新成功，但重启失败: {restart_result.get('error', '未知错误')}",
                    'pull_result': pull_result
                })
        else:
            # 代码没有更新，不需要重启
            return jsonify({
                'success': True,
                'message': pull_result['message'],
                'pull_result': pull_result,
                'restart_needed': False
            })

    except Exception as e:
        logger.error(f"更新代码并重启失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'更新代码并重启失败: {str(e)}'
        }), 500
