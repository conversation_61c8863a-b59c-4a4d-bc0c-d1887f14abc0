#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码管理服务
用于处理Git操作和应用重启
"""

import os
import sys
import subprocess
import threading
import time
from typing import Dict, Any, Optional, List
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

class CodeManagementService:
    """代码管理服务类"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.git_available = self._check_git_availability()
        # Flask重载标志
        self._reload_requested = False

    def _check_git_availability(self) -> bool:
        """检查Git是否可用"""
        try:
            result = subprocess.run(
                ['git', '--version'],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False

    def _run_git_command(self, command: List[str], timeout: int = 10) -> Dict[str, Any]:
        """运行Git命令的通用方法"""
        if not self.git_available:
            return {'success': False, 'error': 'Git不可用，请确保已安装Git并添加到PATH环境变量'}

        try:
            # 切换到项目根目录
            os.chdir(self.project_root)

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )

            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }

        except subprocess.TimeoutExpired:
            return {'success': False, 'error': f'Git命令执行超时: {" ".join(command)}'}
        except Exception as e:
            return {'success': False, 'error': f'Git命令执行失败: {str(e)}'}

    def get_git_status(self) -> Dict[str, Any]:
        """获取Git状态信息"""
        try:
            # 获取当前分支
            branch_result = self._run_git_command(['git', 'branch', '--show-current'])
            if not branch_result['success']:
                return branch_result
            current_branch = branch_result['stdout'].strip() or "未知"

            # 获取最新提交信息
            commit_result = self._run_git_command(['git', 'log', '-1', '--pretty=format:%H|%an|%ad|%s'])
            commit_info = {}
            if commit_result['success'] and commit_result['stdout']:
                parts = commit_result['stdout'].split('|', 3)
                if len(parts) >= 4:
                    commit_info = {
                        'hash': parts[0][:8],  # 短哈希
                        'author': parts[1],
                        'date': parts[2],
                        'message': parts[3]
                    }

            # 检查是否有未提交的更改
            status_result = self._run_git_command(['git', 'status', '--porcelain'])
            has_changes = bool(status_result.get('stdout', '').strip()) if status_result['success'] else False

            # 检查是否有远程更新（先fetch）
            fetch_result = self._run_git_command(['git', 'fetch'], timeout=30)

            # 比较本地和远程
            behind_result = self._run_git_command(['git', 'rev-list', '--count', f'{current_branch}..origin/{current_branch}'])
            commits_behind = 0
            if behind_result['success'] and behind_result['stdout'].strip().isdigit():
                commits_behind = int(behind_result['stdout'].strip())

            return {
                'success': True,
                'current_branch': current_branch,
                'latest_commit': commit_info,
                'has_local_changes': has_changes,
                'commits_behind': commits_behind,
                'can_pull': commits_behind > 0 and not has_changes,
                'git_available': self.git_available
            }

        except Exception as e:
            logger.error(f"获取Git状态失败: {str(e)}")
            return {'success': False, 'error': f'获取Git状态失败: {str(e)}'}

    def get_git_branch_graph(self) -> Dict[str, Any]:
        """获取Git分支图形化信息"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)

            # 获取分支图形化信息
            graph_result = subprocess.run(
                ['git', 'log', '--graph', '--pretty=format:%h|%an|%ar|%s|%d', '--all', '--max-count=20'],
                capture_output=True,
                text=True,
                timeout=15,
                encoding='utf-8',
                errors='ignore'
            )

            if graph_result.returncode != 0:
                return {'success': False, 'error': f'获取分支图失败: {graph_result.stderr}'}

            # 解析图形化输出
            lines = graph_result.stdout.split('\n')
            graph_data = []

            for line in lines:
                if '|' in line and not line.strip().startswith('*'):
                    continue

                # 分离图形部分和提交信息部分
                if '|' in line:
                    parts = line.split('|', 4)
                    if len(parts) >= 4:
                        # 提取图形前缀
                        graph_prefix = line.split(parts[0])[0] if parts[0] in line else ''

                        commit_data = {
                            'graph': graph_prefix,
                            'hash': parts[0].strip(),
                            'author': parts[1].strip(),
                            'time': parts[2].strip(),
                            'message': parts[3].strip(),
                            'refs': parts[4].strip() if len(parts) > 4 else ''
                        }
                        graph_data.append(commit_data)
                else:
                    # 纯图形行
                    if line.strip():
                        graph_data.append({
                            'graph': line,
                            'is_graph_only': True
                        })

            # 获取所有分支信息
            branches_result = subprocess.run(
                ['git', 'branch', '-a', '--format=%(refname:short)|%(objectname:short)|%(committerdate:relative)'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            branches = []
            if branches_result.returncode == 0:
                for line in branches_result.stdout.split('\n'):
                    if line.strip() and '|' in line:
                        parts = line.split('|', 2)
                        if len(parts) >= 3:
                            branch_name = parts[0].strip()
                            is_current = branch_name.startswith('*')
                            if is_current:
                                branch_name = branch_name[1:].strip()

                            branches.append({
                                'name': branch_name,
                                'hash': parts[1].strip(),
                                'last_commit': parts[2].strip(),
                                'is_current': is_current,
                                'is_remote': branch_name.startswith('origin/')
                            })

            return {
                'success': True,
                'graph_data': graph_data,
                'branches': branches
            }

        except subprocess.TimeoutExpired:
            logger.error("Git分支图获取超时")
            return {'success': False, 'error': 'Git分支图获取超时'}
        except Exception as e:
            logger.error(f"获取Git分支图失败: {str(e)}")
            return {'success': False, 'error': f'获取Git分支图失败: {str(e)}'}

    def pull_latest_code(self) -> Dict[str, Any]:
        """拉取最新代码"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)
            
            # 检查Git状态
            status = self.get_git_status()
            if not status['success']:
                return status
            
            if status['has_local_changes']:
                return {
                    'success': False,
                    'error': '存在未提交的本地更改，无法拉取代码。请先提交或撤销更改。'
                }
            
            if status['commits_behind'] == 0:
                return {
                    'success': True,
                    'message': '代码已是最新版本，无需更新。',
                    'updated': False
                }
            
            # 执行git pull
            logger.info("开始拉取最新代码...")
            pull_result = subprocess.run(
                ['git', 'pull', 'origin', status['current_branch']],
                capture_output=True,
                text=True,
                timeout=60,
                encoding='utf-8',
                errors='ignore'
            )
            
            if pull_result.returncode == 0:
                logger.info("代码拉取成功")
                return {
                    'success': True,
                    'message': '代码更新成功！',
                    'updated': True,
                    'output': pull_result.stdout
                }
            else:
                logger.error(f"代码拉取失败: {pull_result.stderr}")
                return {
                    'success': False,
                    'error': f'代码拉取失败: {pull_result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Git pull操作超时")
            return {'success': False, 'error': 'Git pull操作超时'}
        except Exception as e:
            logger.error(f"拉取代码失败: {str(e)}")
            return {'success': False, 'error': f'拉取代码失败: {str(e)}'}

    def trigger_flask_reload(self) -> Dict[str, Any]:
        """触发Flask应用重载（使用Werkzeug重载机制）"""
        try:
            logger.info("触发Flask应用重载...")

            # 使用Werkzeug的重载机制
            def delayed_reload():
                time.sleep(1)  # 给响应时间返回
                logger.info("执行Flask应用重载...")
                # 使用exit code 3来触发Werkzeug重载器
                # 这是Werkzeug重载器的标准机制
                sys.exit(3)

            # 在后台线程中执行重载
            reload_thread = threading.Thread(target=delayed_reload, daemon=True)
            reload_thread.start()

            return {
                'success': True,
                'message': 'Flask应用重载已触发，应用将在1秒后重载...'
            }

        except Exception as e:
            logger.error(f"触发Flask重载失败: {str(e)}")
            return {'success': False, 'error': f'触发Flask重载失败: {str(e)}'}



    def update_and_reload(self) -> Dict[str, Any]:
        """更新代码并进行Flask重载"""
        try:
            logger.info("开始更新代码并重载...")

            # 先拉取最新代码
            pull_result = self.pull_latest_code()

            if not pull_result['success']:
                return pull_result

            # 如果代码有更新，进行Flask重载
            if pull_result.get('updated', False):
                reload_result = self.trigger_flask_reload()

                return {
                    'success': True,
                    'message': '代码更新成功，Flask应用正在重载...',
                    'pull_result': pull_result,
                    'reload_result': reload_result
                }
            else:
                # 代码没有更新，不需要重载
                return {
                    'success': True,
                    'message': pull_result['message'],
                    'pull_result': pull_result,
                    'reload_needed': False
                }

        except Exception as e:
            logger.error(f"更新代码并重载失败: {str(e)}")
            return {'success': False, 'error': f'更新代码并重载失败: {str(e)}'}

# 创建全局服务实例
code_management_service = CodeManagementService()
