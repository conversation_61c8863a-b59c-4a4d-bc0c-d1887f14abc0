#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码管理服务
用于处理Git操作和应用重启
"""

import os
import sys
import subprocess
import signal
import threading
import time
import importlib
import gc
from typing import Dict, Any, Optional, List
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

class CodeManagementService:
    """代码管理服务类"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.restart_delay = 3  # 重启延迟秒数
        self.reload_modules = [
            'routes', 'services', 'models', 'utils', 'config'
        ]  # 需要热重载的模块
        self.git_available = self._check_git_availability()

    def _check_git_availability(self) -> bool:
        """检查Git是否可用"""
        try:
            result = subprocess.run(
                ['git', '--version'],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False

    def _run_git_command(self, command: List[str], timeout: int = 10) -> Dict[str, Any]:
        """运行Git命令的通用方法"""
        if not self.git_available:
            return {'success': False, 'error': 'Git不可用，请确保已安装Git并添加到PATH环境变量'}

        try:
            # 切换到项目根目录
            os.chdir(self.project_root)

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )

            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }

        except subprocess.TimeoutExpired:
            return {'success': False, 'error': f'Git命令执行超时: {" ".join(command)}'}
        except Exception as e:
            return {'success': False, 'error': f'Git命令执行失败: {str(e)}'}

    def get_git_status(self) -> Dict[str, Any]:
        """获取Git状态信息"""
        try:
            # 获取当前分支
            branch_result = self._run_git_command(['git', 'branch', '--show-current'])
            if not branch_result['success']:
                return branch_result
            current_branch = branch_result['stdout'].strip() or "未知"

            # 获取最新提交信息
            commit_result = self._run_git_command(['git', 'log', '-1', '--pretty=format:%H|%an|%ad|%s'])
            commit_info = {}
            if commit_result['success'] and commit_result['stdout']:
                parts = commit_result['stdout'].split('|', 3)
                if len(parts) >= 4:
                    commit_info = {
                        'hash': parts[0][:8],  # 短哈希
                        'author': parts[1],
                        'date': parts[2],
                        'message': parts[3]
                    }

            # 检查是否有未提交的更改
            status_result = self._run_git_command(['git', 'status', '--porcelain'])
            has_changes = bool(status_result.get('stdout', '').strip()) if status_result['success'] else False

            # 检查是否有远程更新（先fetch）
            fetch_result = self._run_git_command(['git', 'fetch'], timeout=30)

            # 比较本地和远程
            behind_result = self._run_git_command(['git', 'rev-list', '--count', f'{current_branch}..origin/{current_branch}'])
            commits_behind = 0
            if behind_result['success'] and behind_result['stdout'].strip().isdigit():
                commits_behind = int(behind_result['stdout'].strip())

            return {
                'success': True,
                'current_branch': current_branch,
                'latest_commit': commit_info,
                'has_local_changes': has_changes,
                'commits_behind': commits_behind,
                'can_pull': commits_behind > 0 and not has_changes,
                'git_available': self.git_available
            }

        except Exception as e:
            logger.error(f"获取Git状态失败: {str(e)}")
            return {'success': False, 'error': f'获取Git状态失败: {str(e)}'}

    def get_git_branch_graph(self) -> Dict[str, Any]:
        """获取Git分支图形化信息"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)

            # 获取分支图形化信息
            graph_result = subprocess.run(
                ['git', 'log', '--graph', '--pretty=format:%h|%an|%ar|%s|%d', '--all', '--max-count=20'],
                capture_output=True,
                text=True,
                timeout=15,
                encoding='utf-8',
                errors='ignore'
            )

            if graph_result.returncode != 0:
                return {'success': False, 'error': f'获取分支图失败: {graph_result.stderr}'}

            # 解析图形化输出
            lines = graph_result.stdout.split('\n')
            graph_data = []

            for line in lines:
                if '|' in line and not line.strip().startswith('*'):
                    continue

                # 分离图形部分和提交信息部分
                if '|' in line:
                    parts = line.split('|', 4)
                    if len(parts) >= 4:
                        # 提取图形前缀
                        graph_prefix = line.split(parts[0])[0] if parts[0] in line else ''

                        commit_data = {
                            'graph': graph_prefix,
                            'hash': parts[0].strip(),
                            'author': parts[1].strip(),
                            'time': parts[2].strip(),
                            'message': parts[3].strip(),
                            'refs': parts[4].strip() if len(parts) > 4 else ''
                        }
                        graph_data.append(commit_data)
                else:
                    # 纯图形行
                    if line.strip():
                        graph_data.append({
                            'graph': line,
                            'is_graph_only': True
                        })

            # 获取所有分支信息
            branches_result = subprocess.run(
                ['git', 'branch', '-a', '--format=%(refname:short)|%(objectname:short)|%(committerdate:relative)'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            branches = []
            if branches_result.returncode == 0:
                for line in branches_result.stdout.split('\n'):
                    if line.strip() and '|' in line:
                        parts = line.split('|', 2)
                        if len(parts) >= 3:
                            branch_name = parts[0].strip()
                            is_current = branch_name.startswith('*')
                            if is_current:
                                branch_name = branch_name[1:].strip()

                            branches.append({
                                'name': branch_name,
                                'hash': parts[1].strip(),
                                'last_commit': parts[2].strip(),
                                'is_current': is_current,
                                'is_remote': branch_name.startswith('origin/')
                            })

            return {
                'success': True,
                'graph_data': graph_data,
                'branches': branches
            }

        except subprocess.TimeoutExpired:
            logger.error("Git分支图获取超时")
            return {'success': False, 'error': 'Git分支图获取超时'}
        except Exception as e:
            logger.error(f"获取Git分支图失败: {str(e)}")
            return {'success': False, 'error': f'获取Git分支图失败: {str(e)}'}

    def pull_latest_code(self) -> Dict[str, Any]:
        """拉取最新代码"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)
            
            # 检查Git状态
            status = self.get_git_status()
            if not status['success']:
                return status
            
            if status['has_local_changes']:
                return {
                    'success': False,
                    'error': '存在未提交的本地更改，无法拉取代码。请先提交或撤销更改。'
                }
            
            if status['commits_behind'] == 0:
                return {
                    'success': True,
                    'message': '代码已是最新版本，无需更新。',
                    'updated': False
                }
            
            # 执行git pull
            logger.info("开始拉取最新代码...")
            pull_result = subprocess.run(
                ['git', 'pull', 'origin', status['current_branch']],
                capture_output=True,
                text=True,
                timeout=60,
                encoding='utf-8',
                errors='ignore'
            )
            
            if pull_result.returncode == 0:
                logger.info("代码拉取成功")
                return {
                    'success': True,
                    'message': '代码更新成功！',
                    'updated': True,
                    'output': pull_result.stdout
                }
            else:
                logger.error(f"代码拉取失败: {pull_result.stderr}")
                return {
                    'success': False,
                    'error': f'代码拉取失败: {pull_result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Git pull操作超时")
            return {'success': False, 'error': 'Git pull操作超时'}
        except Exception as e:
            logger.error(f"拉取代码失败: {str(e)}")
            return {'success': False, 'error': f'拉取代码失败: {str(e)}'}

    def hot_reload_application(self) -> Dict[str, Any]:
        """热重载应用程序（不重启进程）"""
        try:
            logger.info("开始热重载应用程序...")

            # 收集需要重载的模块
            modules_to_reload = []

            # 查找所有已加载的项目模块
            for module_name, module in list(sys.modules.items()):
                if module is None:
                    continue

                # 检查模块是否属于项目
                try:
                    if hasattr(module, '__file__') and module.__file__:
                        module_path = os.path.abspath(module.__file__)
                        if module_path.startswith(self.project_root):
                            # 排除当前模块，避免递归重载
                            if module_name != __name__:
                                modules_to_reload.append((module_name, module))
                except (AttributeError, TypeError):
                    continue

            # 按依赖顺序重载模块
            reload_count = 0
            failed_modules = []

            # 先重载基础模块
            base_modules = ['config', 'utils', 'models']
            for base_module in base_modules:
                for module_name, module in modules_to_reload:
                    if module_name.startswith(base_module):
                        try:
                            importlib.reload(module)
                            reload_count += 1
                            logger.info(f"重载模块: {module_name}")
                        except Exception as e:
                            failed_modules.append((module_name, str(e)))
                            logger.warning(f"重载模块失败: {module_name}, 错误: {e}")

            # 再重载其他模块
            for module_name, module in modules_to_reload:
                if not any(module_name.startswith(base) for base in base_modules):
                    try:
                        importlib.reload(module)
                        reload_count += 1
                        logger.info(f"重载模块: {module_name}")
                    except Exception as e:
                        failed_modules.append((module_name, str(e)))
                        logger.warning(f"重载模块失败: {module_name}, 错误: {e}")

            # 强制垃圾回收
            gc.collect()

            result_message = f"热重载完成！成功重载 {reload_count} 个模块"
            if failed_modules:
                result_message += f"，{len(failed_modules)} 个模块重载失败"

            logger.info(result_message)

            return {
                'success': True,
                'message': result_message,
                'reloaded_count': reload_count,
                'failed_modules': failed_modules
            }

        except Exception as e:
            logger.error(f"热重载失败: {str(e)}")
            return {'success': False, 'error': f'热重载失败: {str(e)}'}

    def restart_application(self) -> Dict[str, Any]:
        """重启应用程序"""
        try:
            logger.info("准备重启应用程序...")
            
            # 延迟重启，给客户端时间接收响应
            def delayed_restart():
                time.sleep(self.restart_delay)
                logger.info("正在重启应用程序...")
                
                # 获取当前进程ID
                current_pid = os.getpid()
                
                # 在Windows系统上使用不同的重启方式
                if sys.platform.startswith('win'):
                    # Windows系统重启
                    try:
                        # 启动新进程
                        subprocess.Popen([sys.executable] + sys.argv, 
                                       cwd=self.project_root,
                                       creationflags=subprocess.CREATE_NEW_CONSOLE)
                        # 终止当前进程
                        os._exit(0)
                    except Exception as e:
                        logger.error(f"Windows重启失败: {e}")
                        os._exit(1)
                else:
                    # Linux/Unix系统重启
                    try:
                        # 发送SIGTERM信号给当前进程
                        os.kill(current_pid, signal.SIGTERM)
                    except Exception as e:
                        logger.error(f"Linux重启失败: {e}")
                        os._exit(1)
            
            # 在后台线程中执行重启
            restart_thread = threading.Thread(target=delayed_restart, daemon=True)
            restart_thread.start()
            
            return {
                'success': True,
                'message': f'应用程序将在{self.restart_delay}秒后重启...'
            }
            
        except Exception as e:
            logger.error(f"重启应用程序失败: {str(e)}")
            return {'success': False, 'error': f'重启应用程序失败: {str(e)}'}

    def update_and_hot_reload(self) -> Dict[str, Any]:
        """更新代码并进行热重载"""
        try:
            logger.info("开始更新代码并热重载...")

            # 先拉取最新代码
            pull_result = self.pull_latest_code()

            if not pull_result['success']:
                return pull_result

            # 如果代码有更新，进行热重载
            if pull_result.get('updated', False):
                reload_result = self.hot_reload_application()

                if reload_result['success']:
                    return {
                        'success': True,
                        'message': '代码更新并热重载成功！',
                        'pull_result': pull_result,
                        'reload_result': reload_result
                    }
                else:
                    # 热重载失败，建议重启
                    return {
                        'success': False,
                        'error': f"代码更新成功，但热重载失败: {reload_result.get('error', '未知错误')}。建议手动重启应用。",
                        'pull_result': pull_result,
                        'reload_result': reload_result,
                        'suggest_restart': True
                    }
            else:
                # 代码没有更新，不需要重载
                return {
                    'success': True,
                    'message': pull_result['message'],
                    'pull_result': pull_result,
                    'reload_needed': False
                }

        except Exception as e:
            logger.error(f"更新代码并热重载失败: {str(e)}")
            return {'success': False, 'error': f'更新代码并热重载失败: {str(e)}'}

# 创建全局服务实例
code_management_service = CodeManagementService()
