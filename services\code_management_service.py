#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码管理服务
用于处理Git操作和应用重启
"""

import os
import sys
import subprocess
import signal
import threading
import time
from typing import Dict, Any, Optional
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

class CodeManagementService:
    """代码管理服务类"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.restart_delay = 3  # 重启延迟秒数
        
    def get_git_status(self) -> Dict[str, Any]:
        """获取Git状态信息"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)
            
            # 获取当前分支
            branch_result = subprocess.run(
                ['git', 'branch', '--show-current'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            current_branch = branch_result.stdout.strip() if branch_result.returncode == 0 else "未知"
            
            # 获取最新提交信息
            commit_result = subprocess.run(
                ['git', 'log', '-1', '--pretty=format:%H|%an|%ad|%s'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            
            commit_info = {}
            if commit_result.returncode == 0 and commit_result.stdout:
                parts = commit_result.stdout.split('|', 3)
                if len(parts) >= 4:
                    commit_info = {
                        'hash': parts[0][:8],  # 短哈希
                        'author': parts[1],
                        'date': parts[2],
                        'message': parts[3]
                    }
            
            # 检查是否有未提交的更改
            status_result = subprocess.run(
                ['git', 'status', '--porcelain'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            has_changes = bool(status_result.stdout.strip()) if status_result.returncode == 0 else False
            
            # 检查是否有远程更新
            fetch_result = subprocess.run(
                ['git', 'fetch', '--dry-run'],
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            # 比较本地和远程
            behind_result = subprocess.run(
                ['git', 'rev-list', '--count', f'{current_branch}..origin/{current_branch}'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            commits_behind = 0
            if behind_result.returncode == 0 and behind_result.stdout.strip().isdigit():
                commits_behind = int(behind_result.stdout.strip())
            
            return {
                'success': True,
                'current_branch': current_branch,
                'latest_commit': commit_info,
                'has_local_changes': has_changes,
                'commits_behind': commits_behind,
                'can_pull': commits_behind > 0 and not has_changes
            }
            
        except subprocess.TimeoutExpired:
            logger.error("Git操作超时")
            return {'success': False, 'error': 'Git操作超时'}
        except Exception as e:
            logger.error(f"获取Git状态失败: {str(e)}")
            return {'success': False, 'error': f'获取Git状态失败: {str(e)}'}
    
    def pull_latest_code(self) -> Dict[str, Any]:
        """拉取最新代码"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)
            
            # 检查Git状态
            status = self.get_git_status()
            if not status['success']:
                return status
            
            if status['has_local_changes']:
                return {
                    'success': False,
                    'error': '存在未提交的本地更改，无法拉取代码。请先提交或撤销更改。'
                }
            
            if status['commits_behind'] == 0:
                return {
                    'success': True,
                    'message': '代码已是最新版本，无需更新。',
                    'updated': False
                }
            
            # 执行git pull
            logger.info("开始拉取最新代码...")
            pull_result = subprocess.run(
                ['git', 'pull', 'origin', status['current_branch']],
                capture_output=True,
                text=True,
                timeout=60,
                encoding='utf-8',
                errors='ignore'
            )
            
            if pull_result.returncode == 0:
                logger.info("代码拉取成功")
                return {
                    'success': True,
                    'message': '代码更新成功！',
                    'updated': True,
                    'output': pull_result.stdout
                }
            else:
                logger.error(f"代码拉取失败: {pull_result.stderr}")
                return {
                    'success': False,
                    'error': f'代码拉取失败: {pull_result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            logger.error("Git pull操作超时")
            return {'success': False, 'error': 'Git pull操作超时'}
        except Exception as e:
            logger.error(f"拉取代码失败: {str(e)}")
            return {'success': False, 'error': f'拉取代码失败: {str(e)}'}
    
    def restart_application(self) -> Dict[str, Any]:
        """重启应用程序"""
        try:
            logger.info("准备重启应用程序...")
            
            # 延迟重启，给客户端时间接收响应
            def delayed_restart():
                time.sleep(self.restart_delay)
                logger.info("正在重启应用程序...")
                
                # 获取当前进程ID
                current_pid = os.getpid()
                
                # 在Windows系统上使用不同的重启方式
                if sys.platform.startswith('win'):
                    # Windows系统重启
                    try:
                        # 启动新进程
                        subprocess.Popen([sys.executable] + sys.argv, 
                                       cwd=self.project_root,
                                       creationflags=subprocess.CREATE_NEW_CONSOLE)
                        # 终止当前进程
                        os._exit(0)
                    except Exception as e:
                        logger.error(f"Windows重启失败: {e}")
                        os._exit(1)
                else:
                    # Linux/Unix系统重启
                    try:
                        # 发送SIGTERM信号给当前进程
                        os.kill(current_pid, signal.SIGTERM)
                    except Exception as e:
                        logger.error(f"Linux重启失败: {e}")
                        os._exit(1)
            
            # 在后台线程中执行重启
            restart_thread = threading.Thread(target=delayed_restart, daemon=True)
            restart_thread.start()
            
            return {
                'success': True,
                'message': f'应用程序将在{self.restart_delay}秒后重启...'
            }
            
        except Exception as e:
            logger.error(f"重启应用程序失败: {str(e)}")
            return {'success': False, 'error': f'重启应用程序失败: {str(e)}'}

# 创建全局服务实例
code_management_service = CodeManagementService()
