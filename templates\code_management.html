{% extends "base.html" %}

{% block title %}代码管理 - OTA设备管理系统{% endblock %}

{% block styles %}
<style>
    .code-management-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .status-card {
        background: var(--card-background);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .status-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(0,123,255,0.1);
    }
    
    .status-icon {
        font-size: 2rem;
        margin-right: 15px;
        color: var(--primary-color);
    }
    
    .status-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    
    .status-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: rgba(248,249,250,0.8);
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
    }
    
    .info-label {
        font-weight: 600;
        color: #666;
        margin-right: 10px;
        min-width: 80px;
    }
    
    .info-value {
        color: #333;
        font-family: 'Courier New', monospace;
    }
    
    .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .badge-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .badge-warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .badge-danger {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        margin-top: 20px;
    }
    
    .action-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 160px;
        justify-content: center;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    }
    
    .btn-success {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        box-shadow: 0 4px 15px rgba(40,167,69,0.3);
    }
    
    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40,167,69,0.4);
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        box-shadow: 0 4px 15px rgba(255,193,7,0.3);
    }
    
    .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255,193,7,0.4);
    }
    
    .btn-disabled {
        background: #6c757d;
        color: white;
        cursor: not-allowed;
        opacity: 0.6;
    }
    
    .btn-disabled:hover {
        transform: none;
        box-shadow: none;
    }
    
    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .log-output {
        background: #1e1e1e;
        color: #f8f8f2;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        max-height: 300px;
        overflow-y: auto;
        margin-top: 15px;
        border: 1px solid #444;
    }
    
    .commit-info {
        background: rgba(0,123,255,0.05);
        border: 1px solid rgba(0,123,255,0.2);
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }
    
    .commit-hash {
        font-family: 'Courier New', monospace;
        background: rgba(0,0,0,0.1);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .git-graph-container {
        background: #1e1e1e;
        color: #f8f8f2;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        max-height: 400px;
        overflow-y: auto;
        margin-top: 15px;
        border: 1px solid #444;
    }

    .git-graph-line {
        white-space: pre;
        line-height: 1.4;
        margin: 2px 0;
    }

    .commit-line {
        display: flex;
        align-items: center;
        padding: 2px 0;
    }

    .commit-graph {
        color: #61dafb;
        margin-right: 10px;
        min-width: 100px;
    }

    .commit-info {
        flex: 1;
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .commit-hash-graph {
        color: #ffd700;
        font-weight: bold;
    }

    .commit-author {
        color: #98d982;
    }

    .commit-time {
        color: #ff9f43;
    }

    .commit-message {
        color: #f8f8f2;
        flex: 1;
    }

    .commit-refs {
        color: #ff6b6b;
        font-weight: bold;
    }

    .branches-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 10px;
        margin-top: 15px;
    }

    .branch-item {
        background: rgba(248,249,250,0.8);
        border-radius: 8px;
        padding: 10px;
        border-left: 4px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .branch-current {
        border-left-color: #28a745;
        background: rgba(40,167,69,0.1);
    }

    .branch-remote {
        border-left-color: #ffc107;
        background: rgba(255,193,7,0.1);
    }

    .branch-icon {
        font-size: 1.2rem;
    }

    .branch-info {
        flex: 1;
    }

    .branch-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
    }

    .branch-details {
        font-size: 0.8rem;
        color: #666;
        font-family: 'Courier New', monospace;
    }
</style>
{% endblock %}

{% block content %}
<div class="code-management-container">
    <div class="status-card">
        <div class="status-header">
            <i class="fas fa-code-branch status-icon"></i>
            <h2 class="status-title">Git 仓库状态</h2>
        </div>
        
        <div id="git-status-content">
            {% if git_status.success %}
            <div class="status-info">
                <div class="info-item">
                    <span class="info-label">当前分支:</span>
                    <span class="info-value">{{ git_status.current_branch }}</span>
                </div>
                
                {% if git_status.latest_commit %}
                <div class="info-item">
                    <span class="info-label">最新提交:</span>
                    <span class="info-value commit-hash">{{ git_status.latest_commit.hash }}</span>
                </div>
                {% endif %}
                
                <div class="info-item">
                    <span class="info-label">本地更改:</span>
                    <span class="status-badge {% if git_status.has_local_changes %}badge-warning{% else %}badge-success{% endif %}">
                        {% if git_status.has_local_changes %}有未提交更改{% else %}无更改{% endif %}
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">远程状态:</span>
                    <span class="status-badge {% if git_status.commits_behind > 0 %}badge-warning{% else %}badge-success{% endif %}">
                        {% if git_status.commits_behind > 0 %}落后 {{ git_status.commits_behind }} 个提交{% else %}已是最新{% endif %}
                    </span>
                </div>
            </div>
            
            {% if git_status.latest_commit %}
            <div class="commit-info">
                <h5><i class="fas fa-info-circle"></i> 最新提交信息</h5>
                <p><strong>作者:</strong> {{ git_status.latest_commit.author }}</p>
                <p><strong>时间:</strong> {{ git_status.latest_commit.date }}</p>
                <p><strong>消息:</strong> {{ git_status.latest_commit.message }}</p>
            </div>
            {% endif %}
            {% else %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                获取Git状态失败: {{ git_status.error }}
            </div>
            {% endif %}
        </div>
    </div>

    <div class="status-card">
        <div class="status-header">
            <i class="fas fa-project-diagram status-icon"></i>
            <h2 class="status-title">Git 分支图</h2>
        </div>

        <div id="git-graph-content">
            <div class="branches-container" id="branches-list">
                <!-- 分支列表将通过JavaScript动态加载 -->
            </div>

            <div class="git-graph-container" id="git-graph">
                <!-- Git图形将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <div class="status-card">
        <div class="status-header">
            <i class="fas fa-cogs status-icon"></i>
            <h2 class="status-title">代码管理操作</h2>
        </div>
        
        <div class="action-buttons">
            <button id="refresh-status-btn" class="action-btn btn-primary">
                <i class="fas fa-sync-alt"></i>
                <span class="loading-spinner"></span>
                刷新状态
            </button>
            
            <button id="pull-code-btn" class="action-btn btn-success" 
                    {% if not git_status.success or not git_status.can_pull %}disabled{% endif %}>
                <i class="fas fa-download"></i>
                <span class="loading-spinner"></span>
                拉取最新代码
            </button>
            
            <button id="restart-app-btn" class="action-btn btn-warning">
                <i class="fas fa-redo"></i>
                <span class="loading-spinner"></span>
                重启应用
            </button>
            
            <button id="hot-reload-btn" class="action-btn btn-success">
                <i class="fas fa-fire"></i>
                <span class="loading-spinner"></span>
                热重载
            </button>

            <button id="update-hot-reload-btn" class="action-btn btn-primary"
                    {% if not git_status.success %}disabled{% endif %}>
                <i class="fas fa-magic"></i>
                <span class="loading-spinner"></span>
                更新并热重载
            </button>

            <button id="update-restart-btn" class="action-btn btn-warning"
                    {% if not git_status.success %}disabled{% endif %}>
                <i class="fas fa-rocket"></i>
                <span class="loading-spinner"></span>
                更新并重启
            </button>
        </div>
        
        <div id="operation-output" style="display: none;">
            <h5><i class="fas fa-terminal"></i> 操作输出</h5>
            <div id="log-content" class="log-output"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='libs/js/jquery.min.js') }}"></script>
<script>
$(document).ready(function() {
    // 显示加载状态
    function showLoading(button) {
        const spinner = button.find('.loading-spinner');
        const icon = button.find('i:not(.loading-spinner)');

        button.prop('disabled', true);
        icon.hide();
        spinner.show();
    }

    // 隐藏加载状态
    function hideLoading(button) {
        const spinner = button.find('.loading-spinner');
        const icon = button.find('i:not(.loading-spinner)');

        button.prop('disabled', false);
        spinner.hide();
        icon.show();
    }

    // 显示操作输出
    function showOutput(content, isError = false) {
        const outputDiv = $('#operation-output');
        const logContent = $('#log-content');

        if (isError) {
            logContent.html('<span style="color: #ff6b6b;">[错误] ' + content + '</span>');
        } else {
            logContent.html('<span style="color: #51cf66;">[信息] ' + content + '</span>');
        }

        outputDiv.show();

        // 滚动到输出区域
        $('html, body').animate({
            scrollTop: outputDiv.offset().top - 100
        }, 500);
    }

    // 加载Git分支图
    function loadGitBranchGraph() {
        $.get('/code_management/api/git_branch_graph')
            .done(function(data) {
                if (data.success) {
                    displayBranches(data.branches);
                    displayGitGraph(data.graph_data);
                } else {
                    $('#git-graph').html('<div style="color: #ff6b6b;">获取分支图失败: ' + data.error + '</div>');
                }
            })
            .fail(function(xhr) {
                $('#git-graph').html('<div style="color: #ff6b6b;">请求失败: ' + xhr.responseText + '</div>');
            });
    }

    // 显示分支列表
    function displayBranches(branches) {
        const branchesContainer = $('#branches-list');
        branchesContainer.empty();

        if (branches && branches.length > 0) {
            branches.forEach(function(branch) {
                let branchClass = 'branch-item';
                let icon = 'fas fa-code-branch';

                if (branch.is_current) {
                    branchClass += ' branch-current';
                    icon = 'fas fa-star';
                } else if (branch.is_remote) {
                    branchClass += ' branch-remote';
                    icon = 'fas fa-cloud';
                }

                const branchElement = $(`
                    <div class="${branchClass}">
                        <i class="${icon} branch-icon"></i>
                        <div class="branch-info">
                            <div class="branch-name">${branch.name}</div>
                            <div class="branch-details">${branch.hash} • ${branch.last_commit}</div>
                        </div>
                    </div>
                `);

                branchesContainer.append(branchElement);
            });
        }
    }

    // 显示Git图形
    function displayGitGraph(graphData) {
        const graphContainer = $('#git-graph');
        graphContainer.empty();

        if (graphData && graphData.length > 0) {
            graphData.forEach(function(item) {
                if (item.is_graph_only) {
                    // 纯图形行
                    const graphLine = $('<div class="git-graph-line"></div>').text(item.graph);
                    graphContainer.append(graphLine);
                } else {
                    // 提交信息行
                    const commitLine = $(`
                        <div class="commit-line">
                            <div class="commit-graph">${item.graph}</div>
                            <div class="commit-info">
                                <span class="commit-hash-graph">${item.hash}</span>
                                <span class="commit-author">${item.author}</span>
                                <span class="commit-time">${item.time}</span>
                                <span class="commit-message">${item.message}</span>
                                ${item.refs ? '<span class="commit-refs">' + item.refs + '</span>' : ''}
                            </div>
                        </div>
                    `);
                    graphContainer.append(commitLine);
                }
            });
        } else {
            graphContainer.html('<div style="color: #666;">暂无提交历史</div>');
        }
    }

    // 刷新Git状态
    function refreshGitStatus() {
        const button = $('#refresh-status-btn');
        showLoading(button);

        $.get('/code_management/api/git_status')
            .done(function(data) {
                if (data.success) {
                    // 重新加载页面以更新状态显示
                    location.reload();
                } else {
                    showOutput('获取Git状态失败: ' + data.error, true);
                }
            })
            .fail(function(xhr) {
                showOutput('请求失败: ' + xhr.responseText, true);
            })
            .always(function() {
                hideLoading(button);
            });
    }

    // 拉取最新代码
    function pullLatestCode() {
        const button = $('#pull-code-btn');
        showLoading(button);

        $.post('/code_management/api/pull_code')
            .done(function(data) {
                if (data.success) {
                    showOutput(data.message);
                    if (data.updated) {
                        // 如果代码有更新，刷新状态
                        setTimeout(refreshGitStatus, 1000);
                    }
                } else {
                    showOutput('拉取代码失败: ' + data.error, true);
                }
            })
            .fail(function(xhr) {
                showOutput('请求失败: ' + xhr.responseText, true);
            })
            .always(function() {
                hideLoading(button);
            });
    }

    // 重启应用程序
    function restartApplication() {
        if (!confirm('确定要重启应用程序吗？这将中断当前所有连接。')) {
            return;
        }

        const button = $('#restart-app-btn');
        showLoading(button);

        $.post('/code_management/api/restart_app')
            .done(function(data) {
                if (data.success) {
                    showOutput(data.message);
                    // 显示倒计时
                    let countdown = 5;
                    const countdownInterval = setInterval(function() {
                        showOutput(data.message + ' 页面将在 ' + countdown + ' 秒后自动刷新...');
                        countdown--;
                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            location.reload();
                        }
                    }, 1000);
                } else {
                    showOutput('重启应用失败: ' + data.error, true);
                }
            })
            .fail(function(xhr) {
                showOutput('请求失败: ' + xhr.responseText, true);
            })
            .always(function() {
                hideLoading(button);
            });
    }

    // 热重载应用程序
    function hotReloadApplication() {
        if (!confirm('确定要热重载应用程序吗？这将重新加载所有Python模块。')) {
            return;
        }

        const button = $('#hot-reload-btn');
        showLoading(button);

        $.post('/code_management/api/hot_reload')
            .done(function(data) {
                if (data.success) {
                    showOutput(data.message + ' 重载了 ' + data.reloaded_count + ' 个模块。');
                    if (data.failed_modules && data.failed_modules.length > 0) {
                        showOutput('部分模块重载失败，详情请查看日志。', true);
                    }
                } else {
                    showOutput('热重载失败: ' + data.error, true);
                }
            })
            .fail(function(xhr) {
                showOutput('请求失败: ' + xhr.responseText, true);
            })
            .always(function() {
                hideLoading(button);
            });
    }

    // 更新代码并热重载
    function updateAndHotReload() {
        if (!confirm('确定要更新代码并热重载吗？这将拉取最新代码并重新加载模块。')) {
            return;
        }

        const button = $('#update-hot-reload-btn');
        showLoading(button);

        $.post('/code_management/api/update_and_hot_reload')
            .done(function(data) {
                if (data.success) {
                    showOutput(data.message);
                    if (data.reload_result) {
                        showOutput('重载了 ' + data.reload_result.reloaded_count + ' 个模块。');
                    }
                    // 刷新状态和分支图
                    setTimeout(function() {
                        refreshGitStatus();
                        loadGitBranchGraph();
                    }, 1000);
                } else {
                    showOutput('更新并热重载失败: ' + data.error, true);
                    if (data.suggest_restart) {
                        showOutput('建议使用"更新并重启"功能。', true);
                    }
                }
            })
            .fail(function(xhr) {
                showOutput('请求失败: ' + xhr.responseText, true);
            })
            .always(function() {
                hideLoading(button);
            });
    }

    // 更新代码并重启
    function updateAndRestart() {
        if (!confirm('确定要更新代码并重启应用程序吗？这将中断当前所有连接。')) {
            return;
        }

        const button = $('#update-restart-btn');
        showLoading(button);

        $.post('/code_management/api/update_and_restart')
            .done(function(data) {
                if (data.success) {
                    showOutput(data.message);
                    if (data.restart_result) {
                        // 显示倒计时
                        let countdown = 5;
                        const countdownInterval = setInterval(function() {
                            showOutput(data.message + ' 页面将在 ' + countdown + ' 秒后自动刷新...');
                            countdown--;
                            if (countdown < 0) {
                                clearInterval(countdownInterval);
                                location.reload();
                            }
                        }, 1000);
                    } else {
                        // 只是更新了代码，刷新状态
                        setTimeout(refreshGitStatus, 1000);
                    }
                } else {
                    showOutput('更新并重启失败: ' + data.error, true);
                }
            })
            .fail(function(xhr) {
                showOutput('请求失败: ' + xhr.responseText, true);
            })
            .always(function() {
                hideLoading(button);
            });
    }

    // 绑定按钮事件
    $('#refresh-status-btn').click(refreshGitStatus);
    $('#pull-code-btn').click(pullLatestCode);
    $('#restart-app-btn').click(restartApplication);
    $('#hot-reload-btn').click(hotReloadApplication);
    $('#update-hot-reload-btn').click(updateAndHotReload);
    $('#update-restart-btn').click(updateAndRestart);

    // 初始化加载Git分支图
    loadGitBranchGraph();

    // 定期刷新状态（每30秒）
    setInterval(function() {
        // 只有在没有正在进行的操作时才自动刷新
        if (!$('.action-btn:disabled').length) {
            refreshGitStatus();
            loadGitBranchGraph();
        }
    }, 30000);
});
</script>
{% endblock %}
